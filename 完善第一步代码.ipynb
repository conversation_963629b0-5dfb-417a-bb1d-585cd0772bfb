# AI语音反馈教学助手：语音转文字 + GPT语法纠错 + AI朗读反馈
import openai
from openai import OpenAI
import whisper
import pyttsx3
import sounddevice as sd
import numpy as np
import scipy.io.wavfile as wav
import os
import warnings
import tempfile
import queue
import ipywidgets as widgets
from IPython.display import display, Markdown, HTML
import contextlib
import sys
import logging
import re
from bs4 import BeautifulSoup


warnings.filterwarnings('ignore')
logging.getLogger("whisper.transcribe").setLevel(logging.ERROR)

# === 初始化模块 ===
client = OpenAI(
    api_key="********************************************************************************************************************************************************************",
    base_url="https://api.openai.com/v1",
)

import torch  
model = whisper.load_model("small.en", device="cuda" if torch.cuda.is_available() else "cpu")

engine = pyttsx3.init()
engine.setProperty('rate', 150)
engine.setProperty('volume', 1.0)


# === 文本转语音 ===
def text_to_speech(text):
    try:
        engine.say(text)
        engine.runAndWait()
    except Exception as e:
        print("❌ 播报失败：", e)
        
def speak_visible_english(html: str):
    EMOJI = re.compile(r"[\U0001F300-\U0001FAFF\u2600-\u27BF]+")
    IPA   = re.compile(r"/[^/\n]{1,20}/")
    CJK   = re.compile(r"[\u4e00-\u9fff]")
    ONLY_SYMBOLS = re.compile(r"^[^A-Za-z0-9]+$")

    soup = BeautifulSoup(html, "html.parser")
    for x in soup.select("span.translation, [hidden], [aria-hidden='true'], [style*='display:none'], button, [role='button'], [class*='btn'], [class*='button'], [class*='icon'], .material-icons, [class*='fa-'], [class*='mdi-'], [class*='lucide-'], svg, i, canvas"):
        x.decompose()
    for t in soup(["script","style","noscript"]): t.decompose()

    txt = soup.get_text("\n")
    txt = EMOJI.sub("", txt)
    txt = IPA.sub("", txt)
    lines = [re.sub(r"\s+", " ", ln).strip() for ln in txt.splitlines()]
    lines = [ln for ln in lines if ln and not CJK.search(ln) and not ONLY_SYMBOLS.match(ln) and re.search(r"[A-Za-z]", ln)]
    for ln in lines:
        text_to_speech(ln)  # 直接用你已有的 speak()

    
# === Whisper 识别 ===
def transcribe_audio(filename="input.wav"):
    try:
        with contextlib.redirect_stdout(open(os.devnull, "w")), contextlib.redirect_stderr(open(os.devnull, "w")):
            result = model.transcribe(filename, language="en", fp16=False, verbose=False)
        return result.get("text", "")
    except Exception as e:
        print("❌ 识别失败：", e)
        return ""
    finally:
        os.remove(filename)

# === GPT 反馈 ===
def get_feedback_response(text):
    prompt = """你是一位友善且温暖的英语口语教师，请根据以下对话者的英语口语内容判断其语法和语音是否正确。
    
如果语法和语音都正确：
- 用英文表达其语法语音都正确，并给出多样化的表扬语，随机多变的匹配相应的鼓励性的表情包，如：点赞、鼓掌等。
- 根据对话内容，用英文自然地回应。

如果存在语法和语音错误：
- 请将英文中有误的句子加粗并显示为红色，格式如下：
<strong style='color:red;'>错误句子</strong>
- 根据对话内容，用英文自然回应。
- 最后用英文鼓励其继续努力，鼓励语要丰富，随机加点鼓励性表情包。

Visible text must be English only. Never output Chinese outside <span class='translation' style='display:none'>…</span>.
Always include both headings: Grammar and Pronunciation. If no reliable issue, say “Your pronunciation is correct.”
Your Sentences: reproduce the learner’s input verbatim (word-for-word). Do not correct or normalize anything there; only add <br> between sentences and the hidden translation spans. No duplication elsewhere.
No placeholders: never output “N/A”, braces, or template tokens.
Grammar:
1) Presence and heading
- The Grammar section must ALWAYS appear under “Language Feedback”.
- Use this exact heading: <strong style="color:red;">Grammar</strong>:
- Visible text must be ENGLISH ONLY. Put any Chinese ONLY inside a hidden span or div:
  <span class='translation' style='display:none;'>(…)</span>
  or <div class="translation" style="display:none;">(…)</div>
2) When errors exist (incorrect branch)
- After the heading, output EXACTLY one black line: {{Grammar_issue_line_en}}
  Set {{Grammar_issue_line_en}} based on error_count:
    • 1  → "There is a grammar issue."
    • ≥2 → "There are grammar issues."
- Output Grammar_issue_line_en exactly. Do not repeat or rephrase this line elsewhere.
- Then enumerate ALL distinct errors (no upper limit) as a numbered list.
  Numbering must start at [1] and increase with NO gaps.
- Each list item MUST follow this format (use straight double quotes):
  [n] "WRONG" should be "<strong style="color:red;">RIGHT</strong>" — Reason: clear English explanation.
  The WRONG or RIGHT can be a word, phrase, or clause. The Reason may be 1–2 sentences
  in student-friendly English.
- Diff completeness rule: first compose the corrected version, then ensure EVERY change
  between the learner’s text and the corrected version appears once in the list.
  Must cover (not limited to):
  • Pronoun gender and case (he, him, his vs she, her)
  • Subject–auxiliary agreement (am, is, are; was, were)
  • Tense and intention (current intention uses “I want to …”; if “I wanted to …” is not a past desire, correct to “I want to …”)
  • Past-time adverbials → simple past
  • Verb patterns (help + base form; look forward to + gerund; suggest + clause or gerund, etc.)
  • Articles and countability
  • Prepositions
  • Word choice and word order
  • Fragments and run-ons
  • Punctuation and capitalization
- After the numbered list, output EXACTLY one line that includes ONLY the sentences
  that had errors (do NOT include sentences that were already correct):
  It should be: "<strong style="color:red;">CORRECTED_ERROR_SENTENCES_ONLY</strong>"
3) Chinese translation block (one block at the end)
- Immediately after the “It should be:” line, output ONE hidden Chinese block that first
  gives the translation of the corrected sentence(s), then numbered translations matching
  [1], [2], [3], … in order, for example:
  <div class="translation" style="display:none;">
  (Corrected translation…)
  ([1] Chinese explanation…)
  ([2] Chinese explanation…)
  ([3] Chinese explanation…)
  </div>
- Do NOT place any other Chinese outside this block.
4) When there are NO errors (correct branch)
- Output ONLY:
  <span style="color:#000;font-weight:400;">Your grammar is correct.</span>
  <div class="translation" style="display:none;">（你的语法是正确的。）</div>
- Do NOT output the numbered list or the “It should be:” line.
5) General constraints
- Preserve the learner’s meaning; do NOT invent new facts (no new names, ages, or events).
- Use straight double quotes "…". Do not use curly-brace characters, placeholder text
  (such as “Reason 1”), or “N/A”.
- Place punctuation inside the quoted corrected text when appropriate.

Pronunciation:
Detect pronunciation issues only from the provided evidence; do not infer beyond data. Default sensitivity: high.
Input
- pron_evidence: array items with word (required), dialect (AmE or BrE; default AmE), obs_ipa (optional), asr_conf (optional 0–1), n_best_tokens (optional array).
- sensitivity: optional, values: high, normal, low.
- Target IPA examples: apple AmE ˈæpəl, BrE ˈæp(ə)l; banana AmE bəˈnænə, BrE bəˈnɑːnə. Parentheses mark optional segments; either form counts as correct.

Rules (per item)
1) Set target_ipa by dialect; accept optional segments either present or absent.
2) If obs_ipa is present: compare at the phoneme level; primary/secondary stress ˈ/ˌ must match. Report exactly one reason, choosing by this priority: Stress misplaced → Vowel error → Consonant error → Cluster deletion → Final consonant omitted or unreleased. When evidence allows, add:
   - detail_en / detail_zh (e.g., “æ→ɑː in stressed syllable”, “primary stress on 1st not 2nd”)
   - phon_note_en / phon_note_zh (e.g., “iː vs ɪ tense–lax; alveolar stop deletion in cluster”)
   - diff_ipa (compact difference like “æ→ɑː”, “/sts/→/s/”, “ˈ→(none)”)
   - stress_note_en / stress_note_zh (if stress differs)
   - tip_en / tip_zh (one actionable cue)
   - minimal_pairs / minimal_pairs_zh (up to three contrasts such as “ship–sheep”)
3) If obs_ipa is missing: apply sensitivity thresholds using asr_conf and n_best_tokens. High: flag when asr_conf < 0.95 or the target is not consistently top-1 in n_best_tokens (less than 70 percent). Normal: 0.88 and 60 percent. Low: 0.80 and 50 percent. If patterns justify, map to vowel change, consonant change, cluster deletion, or final consonant missing; otherwise use "Unclear from ASR" and add a brief asr_note_en / asr_note_zh explaining the uncertainty (e.g., low confidence, unstable top-1).
4) Never fabricate problems; if evidence aligns strongly with the target, do not flag. One reason per item only.

Output (JSON only; compact; exact keys)
- has_pron_issues: boolean.
- If true:
  - Pron_issue_line_en: exactly "There is a pronunciation issue." for one item, or "There are pronunciation issues." for two or more.
  - Pron_issue_line_zh: exactly "存在发音问题。"
  - Pron_items: array in input order; each object includes:
    word;
    target_ipa (IPA without slashes, for example ˈæpəl);
    reason_en: one of "Vowel error" | "Stress misplaced" | "Consonant error" | "Cluster deletion" | "Final consonant omitted/unreleased" | "Unclear from ASR";
    reason_zh: the matching Chinese label.
    Optional fields when available: obs_ipa; diff_ipa; stress_note_en/zh; detail_en/zh; phon_note_en/zh; tip_en/zh; minimal_pairs (array); minimal_pairs_zh (array); asr_note_en/zh.
- If false: return only an object with has_pron_issues set to false.
Formatting: return valid JSON only; no code fences; no extra fields; keys must match exactly.

Respond to the meaning of the corrected sentence(s) only. Write 3–4 concise English sentences that (1) acknowledge, (2) build/relate, (3) give a tiny tip/bridge, (4) end with an open question to keep the conversation going. Do not repeat or paraphrase the learner’s or the corrected sentences, and do not mention grammar, corrections, or give praise/judgment. Render all English on one line inside a single green bold span (no <br>). On the next line, output one hidden Chinese line that concatenates the translations using
<span class='translation' style='display:none;'>(…)</span>.
Comments: after each English sentence, append one encouragement emoji, chosen　differently. No repeats in the section; vary across runs. English on one line (no extra <br>); then one hidden Chinese line with the concatenated translations.
Translations: Put ALL Chinese ONLY inside <span class='translation' style='display:none;'>（…）</span>. Never output Chinese outside that span; no ASCII parentheses. 

Error checklist (run silently; don’t show this list):
Subject–auxiliary agreement (SVA): I→am; you/we/they→are; he/she/it→is. Questions: “What are you doing?”, “Where is she going?” Flag “you is/was”, “what is you doing”, etc.
Time adverbials & tense: yesterday/last week/… ago/last + unit/in 2010/when I was… → simple past.
since/for (period) + up to now/ever/never/already/just/yet → usually present perfect (accept AmE simple past only with specific past time).
Verb form after common triggers: help + base (“help her find”), look forward to V-ing, suggest (that) + clause / V-ing, enjoy V-ing, stop V-ing / to V (meaning differs).
Fragments/run-ons: ensure each sentence has finite verb; fix comma splices briefly.
Articles/determiners & countability: a/an, the/zero, much/many, few/little.
Common word choice confusions: say/tell; speak/talk; borrow/lend; fun/funny; another/other/others; because/because of.
Prepositions (core): at/on/in (time), in/at/on (place), to/for differences.
Pronoun case & agreement: me/I, he/him; everyone is; data (allow modern plural/singular).
Spelling that flips meaning: fight/find; form/from, etc. Correct only in Grammar.
Capitalization & basic punctuation (first-word caps, end punctuation).

Few-shot anchors (do not display):
“I go to Singapore last week.” → “I went to Singapore last week.” (past-time marker)
“What is you doing now?” → “What are you doing now?” (SVA)
“I look forward to see you.” → “I look forward to seeing you.” (to + V-ing)
“I helped her fight her umbrella.” → “I helped her find her umbrella.” (word choice)

请根据以下对话内容，直接生成一段完整的 HTML 卡片。不要输出任何解释或多余文本。不要出现 “N/A”。不要重复回显学习者的句子到多个位置（只在 “Your Sentences” 中展示一次）。

<div style="background:#f9f9f9; border-left:6px solid #2196F3; padding:15px; border-radius:10px; line-height:1.7; font-size:16px; font-family:Arial;">
    <p> 📝 <strong>Your Sentences</strong><br>{text}</p>
    <!-- 逐句“原样复制” {text}，只在句末加 <br>，绝不改词、绝不改时态、绝不自动纠错。>
    <span class='translation' style='display:none;'>（这里是动态生成的中文翻译）</span>
    </p>
    <!-- Language Feedback：始终包含 Grammar 与 Pronunciation 两段 -->
    <p> 🔍 <strong>Language Feedback</strong><br>
    <!-- Grammar（标题红粗；黑字提示；编号逐条；最后只给“出错句”的正确版本） -->
    <strong style="color:red;">Grammar</strong>:
    Grammar_issue_line_en = ('Your grammar is correct.' if error_count == 0 else
    ('There is a grammar issue.' if error_count == 1 else 'There are grammar issues.'))
    <span style="color:#000;font-weight:400;">{{Grammar_issue_line_en}}</span><br>
    <ul style="margin:4px 0 0 18px; padding-left:0; list-style:none;">
    <li>[1] "{{Err1_wrong}}" should be "<strong style="color:red;">{{Err1_right}}</strong>" — Reason: {{Err1_reason_en}}.</li>
    <li>[2] "{{Err2_wrong}}" should be "<strong style="color:red;">{{Err2_right}}</strong>" — Reason: {{Err2_reason_en}}.</li>
    <li>[3] "{{Err3_wrong}}" should be "<strong style="color:red;">{{Err3_right}}</strong>" — Reason: {{Err3_reason_en}}.</li>
    <!-- 如有更多，继续 [4]、[5]…，确保连续编号 -->
    </ul>
    <p style="margin:4px 0 0 0;">
    It should be: "<strong style="color:red;">{{Corrected_sentences_only}}</strong>"
    </p>
    <div class="translation" style="display:none;">
   （存在语法问题。[1] {{Err1_reason_zh}}。[2] {{Err2_reason_zh}}。[3] {{Err3_reason_zh}}。应改为：{{Corrected_sentences_only_zh}}。）
    </div>
    <br>   
    <strong style="color:red;">Pronunciation</strong>:   
    <!-- Pronunciation：标题永远红粗；中文紧跟英文后面；无隐藏翻译块 -->
    <span style="color:red !important; font-weight:700 !important;">Pronunciation</span>:
    {{#has_pron_issues}}<span style="color:#000;font-weight:400;">{{Pron_issue_line_en}} （{{Pron_issue_line_zh}}）</span>{{/has_pron_issues}}
    {{^has_pron_issues}}<span style="color:#000;font-weight:400;">No obvious pronunciation issues. （未发现明显发音问题。）</span>{{/has_pron_issues}}<br>
    {{#has_pron_issues}}
    <ol style="margin:4px 0 0 18px; padding-left:0;">
    {{#Pron_items}}
    <li>
    “{{word}}” should be “<strong style="color:red;">/{{target_ipa}}/</strong>”
    — Reason: {{reason_en}}（{{reason_zh}}）.
    <span style="color:#666;">
      {{#obs_ipa}} Observed: {{obs_ipa}}.{{/obs_ipa}}
      {{#diff_ipa}} Diff: {{diff_ipa}}.{{/diff_ipa}}
      {{#stress_note_en}} Stress: {{stress_note_en}}{{#stress_note_zh}}（{{stress_note_zh}}）{{/stress_note_zh}}.{{/stress_note_en}}
      {{#phon_note_en}} Phonetics: {{phon_note_en}}{{#phon_note_zh}}（{{phon_note_zh}}）{{/phon_note_zh}}.{{/phon_note_en}}
      {{#detail_en}} Detail: {{detail_en}}{{#detail_zh}}（{{detail_zh}}）{{/detail_zh}}.{{/detail_en}}
      {{#tip_en}} Tip: {{tip_en}}{{#tip_zh}}（{{tip_zh}}）{{/tip_zh}}.{{/tip_en}}
      {{#minimal_pairs}} Minimal pairs: {{.}}.{{/minimal_pairs}}
      {{#asr_note_en}} ASR: {{asr_note_en}}{{#asr_note_zh}}（{{asr_note_zh}}）{{/asr_note_zh}}.{{/asr_note_en}}
    </span>
    </li>
    {{/Pron_items}}
    </ol>
    {{/has_pron_issues}}

    <!-- Let's Chat：绿粗、不换行；每句后仍有隐藏中文 -->
    <p> 📞 <strong>Let's Chat</strong><br>
  　<strong style="color:green;">{{R1}} {{R2}} {{R3}} {{R4_OPT}}</strong><br>
 　 <span class='translation' style='display:none;'>（{{R1_zh}} {{R2_zh}} {{R3_zh}} {{R4_OPT_zh}}）</span>
    </p>
    <!-- Comments：单段落；每句结尾附1个鼓励类表情（多变、不重复）；每句后跟隐藏中文 -->
    <p> 🚀 <strong>Comments</strong><br>
  　{{C1}} 🎉 {{C2}} 💪 {{C3_OPT}} 🌟 {{C4_OPT}} 😊<br>
  　<span class='translation' style='display:none;'>（{{C1_zh}} {{C2_zh}} {{C3_OPT_zh}} {{C4_OPT_zh}}）</span>
</div>

翻译标签应统一使用 class='translation' 并设置 display:none；点击下方按钮后统一显示。

按钮格式如下，放置在卡片底部：
<button id='toggleBtn' onclick='toggleTranslation()' style='margin-top:10px; margin-right:10px; float:right; padding:6px 12px; border-radius:6px; background:#3f88c5; color:white; border:none; cursor:pointer;'>🔁 Show Translation</button>
<script>
  var shown = false;
  function toggleTranslation() {{
    var items = document.getElementsByClassName('translation');
    for (var i = 0; i < items.length; i++) {{
      items[i].style.display = shown ? 'none' : 'inline';
    }}
    shown = !shown;
    document.getElementById('toggleBtn').innerText = shown ? '🔁 Hide Translation' : '🔁 Show Translation';
  }}
</script>

在点击“🔁 Show Translation”按钮后每个英文句子后面都必须紧跟对应的中文翻译，并使用如下格式嵌入：<span class='translation' style='display:none;'>（中文翻译）</span>。统一显示，按钮请放置在卡片右下角。
务必确保所有英文句子都带有这样的翻译标签。
请勿遗漏任何翻译内容，确保按钮能控制所有 class 为 translation 的内容显隐。
请不要输出其他内容，只输出完整 HTML 卡片。
""".format(text = text)
    
    
    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {"role": "system", "content": "你是一位友善的英语教师，会用英文判断语法错误并用英文回应学生问题。"},
            {"role": "user", "content": prompt}
        ],
        max_tokens=1200,
        temperature=0.2
    )

    gpt_html = response.choices[0].message.content
    avatar_img = "<img src='files/neza-avatar.png' style='height:100%; max-height:240px; border-radius:20px;'>"  # ✅ 等比例放大哪吒，使其与卡片高度接近并保持居中"<img src='files/neza-avatar.png' height='100%' style='margin-right:16px; border-radius:20px; transform: scale(2); transform-origin: center;'>"  # ✅ 放大哪吒两倍，保持卡片对齐"<img src='files/neza-avatar.png' height='100%' style='margin-right:16px; border-radius:20px;'>"  # ✅ 高度撑满卡片左侧"<img src='files/neza-avatar.png' width='60' style='margin-right:12px; border-radius:50%; vertical-align:top;'>"
    html = """
    <div style='display:flex; align-items:stretch; background:#f0f8ff; border: 4px solid sienna; border-radius:10px; padding:15px;'>
      <div style='width: 110px; display: flex; align-items:center; justify-content:center; padding:8px;'>
        {avatar_img}
      </div>
      <div style='flex:1; padding: 0 12px;'>
        {gpt_html}
      </div>
    </div>
    """.format(avatar_img=avatar_img, gpt_html=gpt_html)
    return html

# === 控制录音类 ===
class ControlledRecorder:
    def __init__(self, samplerate=16000):     
        self.samplerate = samplerate
        self.q = queue.Queue()
        self.recording = []

    def start(self):
        self.recording = []
        self.q = queue.Queue()
        self.recording_output = widgets.Output()
        with self.recording_output:
            display(Markdown("**🎧 Recording, speak now...**"))
        display(self.recording_output)  # ✅ 将提示包装进 Output 容器，便于 stop() 时清除  # ✅ 使用 Markdown 渲染提示，便于后续清除  # ✅ 保留提示，但不再清空所有输出
        self.stream = sd.InputStream(samplerate=self.samplerate, channels=1, dtype='float32', callback=self.callback)
        self.stream.start()
    
    def callback(self, indata, frames, time, status):
        self.q.put(indata.copy())

    def stop(self):
        try:
            self.stream.stop()
        except:
            return None
        while not self.q.empty():
            self.recording.append(self.q.get())
        if not self.recording:
            return None
        try:
            audio = np.concatenate(self.recording, axis=0)
            audio = audio * 1.5  # 增益1.5倍
            audio = np.clip(audio, -1.0, 1.0)  # 防止削波
            
            # 转换为int16格式保存
            audio_int16 = (audio * 32767).astype(np.int16)
        except:
            return None
        f = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
        wav.write(f.name, self.samplerate, audio_int16)
        return f.name

# === 控制按钮函数 ===
def on_start_clicked(b):
    recorder.start()  # ✅ 立即开始录音
    countdown_output = widgets.Output()
    display(countdown_output)
    import time
    for i in range(20, 0, -1):  # ✅ 倒计时 20 秒，与录音时长一致
        with countdown_output:
            countdown_output.clear_output(wait=True)
            display(Markdown(f"<span style='font-size:24px;'>⏳ 正在录音（剩余 {i} 秒）...</span>"))  # ✅ 放大倒计时提示字体
        time.sleep(1)
    countdown_output.clear_output()
    audio_file = recorder.stop()  # ✅ 20 秒后自动结束录音
    if not audio_file:
        return
    text = transcribe_audio(audio_file)
    if not text.strip():
        return
    if "close the conversation" in text.lower():
        print("🛑 手动结束对话。再见！")
        text_to_speech("好的，再见！")
        return
    reply = get_feedback_response(text)
    display(HTML(reply))  # ✅ 渲染 HTML 输出
    speak_visible_english(reply)
    

def on_stop_clicked(b):
    print("🛑 手动结束对话。再见！")
    text_to_speech("好的，再见！")

# === UI ===
recorder = ControlledRecorder(samplerate=16000)
start_button = widgets.Button(description="🟢 Start Recording", button_style="success")
stop_button = widgets.Button(description="🛑 Stop Recording", button_style="danger")

start_button.on_click(on_start_clicked)
stop_button.on_click(on_stop_clicked)

display(widgets.HBox([start_button, stop_button]))



